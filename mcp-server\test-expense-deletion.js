const axios = require('axios');

const SERVER_URL = 'http://localhost:3001';

// Test data
const testUserId = 'test-user-123';
const testFarms = [
  {
    id: 'test-farm-123',
    name: 'Test Farm',
    ownerId: testUserId
  }
];

async function testExpenseDeletionDetection() {
  console.log('🧪 Testing Expense Deletion Detection...\n');

  const testCases = [
    // English deletion commands
    { prompt: 'Delete expense', expected: 'delete_expense' },
    { prompt: 'Remove expense', expected: 'delete_expense' },
    { prompt: 'Remove exp', expected: 'delete_expense' },
    { prompt: 'Vanish expense', expected: 'delete_expense' },
    { prompt: 'Get rid of expense', expected: 'delete_expense' },
    { prompt: 'Delete the feed expense', expected: 'delete_expense' },
    { prompt: 'Remove the 5000 PKR expense', expected: 'delete_expense' },
    
    // Urdu deletion commands
    { prompt: 'خرچہ حذف کریں', expected: 'delete_expense' },
    { prompt: 'اخراجات ہٹا دیں', expected: 'delete_expense' },
    
    // Non-deletion commands (should NOT be detected as deletion)
    { prompt: 'Add expense', expected: 'expense' },
    { prompt: 'Show expenses', expected: 'unknown' },
    { prompt: 'Create new expense', expected: 'expense' },
  ];

  for (const testCase of testCases) {
    try {
      console.log(`📤 Testing: "${testCase.prompt}"`);
      
      const response = await axios.post(`${SERVER_URL}/open-ai-chat`, {
        prompt: testCase.prompt,
        userId: testUserId,
        language: 'en',
        farms: testFarms
      });

      // Check if the response indicates expense deletion was detected
      const isExpenseDeletion = response.data.context?.action === 'delete_expense' ||
                               response.data.message?.includes('Select expense to delete') ||
                               response.data.expenseImages !== undefined;

      if (testCase.expected === 'delete_expense') {
        if (isExpenseDeletion) {
          console.log(`✅ PASS: Correctly detected as expense deletion`);
        } else {
          console.log(`❌ FAIL: Should be detected as expense deletion`);
          console.log(`   Response: ${response.data.message?.substring(0, 100)}...`);
        }
      } else {
        if (!isExpenseDeletion) {
          console.log(`✅ PASS: Correctly NOT detected as expense deletion`);
        } else {
          console.log(`❌ FAIL: Should NOT be detected as expense deletion`);
          console.log(`   Response: ${response.data.message?.substring(0, 100)}...`);
        }
      }
      
      console.log('');
    } catch (error) {
      console.error(`❌ Error testing "${testCase.prompt}":`, error.response?.data?.message || error.message);
      console.log('');
    }
  }
}

async function testExpenseSelectionFlow() {
  console.log('🧪 Testing Expense Selection Flow...\n');

  // First, trigger expense deletion to get the selection list
  try {
    console.log('📤 Step 1: Triggering expense deletion...');
    const deletionResponse = await axios.post(`${SERVER_URL}/open-ai-chat`, {
      prompt: 'Delete expense',
      userId: testUserId,
      language: 'en',
      farms: testFarms
    });

    console.log('📥 Deletion response status:', deletionResponse.status);
    
    if (deletionResponse.data.expenseImages) {
      console.log('✅ Expense selection list received');
      console.log(`   Found ${deletionResponse.data.expenseImages.length} expenses`);
      
      if (deletionResponse.data.expenseImages.length > 0) {
        // Test selecting the first expense
        const firstExpense = deletionResponse.data.expenseImages[0];
        console.log(`📤 Step 2: Selecting expense: ${firstExpense.name}`);
        
        const selectionResponse = await axios.post(`${SERVER_URL}/open-ai-chat`, {
          prompt: firstExpense.id, // Send the expense ID
          userId: testUserId,
          language: 'en',
          context: deletionResponse.data.context,
          farms: testFarms
        });

        console.log('📥 Selection response status:', selectionResponse.status);
        
        if (selectionResponse.data.deleted) {
          console.log('✅ Expense successfully deleted!');
          console.log(`   Deleted: ${selectionResponse.data.expenseData.category}`);
        } else if (selectionResponse.data.error) {
          console.log('⚠️ Expected behavior - expense not found (test data)');
        } else {
          console.log('❌ Unexpected response format');
          console.log('   Response:', JSON.stringify(selectionResponse.data, null, 2));
        }
      }
    } else if (deletionResponse.data.message?.includes('No expenses found')) {
      console.log('✅ Expected behavior - no expenses in test farm');
    } else {
      console.log('❌ Unexpected response format');
      console.log('   Response:', JSON.stringify(deletionResponse.data, null, 2));
    }
    
  } catch (error) {
    console.error('❌ Error in expense selection flow:', error.response?.data || error.message);
  }
}

// Run tests
async function runAllTests() {
  await testExpenseDeletionDetection();
  console.log('\n' + '='.repeat(50) + '\n');
  await testExpenseSelectionFlow();
}

runAllTests();
