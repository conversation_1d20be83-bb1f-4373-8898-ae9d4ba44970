const { analyzeRequestType } = require('./utils/request-analyzer');

// Test the request type analyzer directly
const testCases = [
  'Delete my cow Daisy',
  'Remove animal number 123',
  'I sold the goat named <PERSON> yesterday',
  'The cow died this morning',
  'I need to remove an animal from my list',
  'جانور نمبر 45 مر گیا ہے'
];

console.log('🧪 Testing Request Type Analyzer...\n');

testCases.forEach((prompt, index) => {
  console.log(`${index + 1}. Prompt: "${prompt}"`);
  const requestType = analyzeRequestType(prompt, false);
  console.log(`   Request Type: ${requestType}`);
  console.log('');
});

console.log('✅ Request type analysis completed!');
