.................ADDITION..................
1: Add Farm  DOne
2: Add Animal DOne
3: Health Check  DOne
4: Milking DOne       browni gave 15 liters of milk this morning
5: Expense DOne
6:Pregnancy           Add pregnancy for <PERSON>, status is confirmed, and conception date was yesterday.
7: Task               Create a task to fix the fence
8: Health record      Record vaccination for Daisy with FMD vaccine, administered by Dr. <PERSON> today.

.....................DLETETION.................
1: Delete Farm  
2: Delete Expense 
3: Delete Animal



    SCENARIO 1 :
        User: "Create a task to fix the fence"
        Server: "Who should this task be assigned to?"
        User: Types "john" 
        Server: "Employee 'john' not found"
    SCENARIO 2 :
        User: "Create a task to fix the fence"
        Server: "Who should this task be assigned to?"
        User: Types "john" 
        Server: "Employee 'john' not found"