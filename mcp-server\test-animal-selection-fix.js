const axios = require('axios');

const SERVER_URL = 'http://localhost:3001';

// Test data
const testUserId = 'test-user-123';
const testFarms = [
  {
    id: 'test-farm-123',
    name: 'Test Farm',
    ownerId: testUserId
  }
];

async function testAnimalSelectionForDeletion() {
  console.log('🧪 Testing Animal Selection for Deletion Fix...\n');

  // Simulate clicking on an animal for deletion
  const animalSelectionRequest = {
    prompt: 'qCVCQtgIGDAAMXThmK', // This is the animal ID from your logs
    userId: testUserId,
    language: 'en',
    context: {
      action: 'delete_animal',
      farmId: '7owMeeTfGSYqaRpgIsOE', // Farm ID from your logs
      farmName: 'Haven View.',
      reason: null,
      needsAnimalSelection: true
    },
    farms: testFarms
  };

  try {
    console.log('📤 Sending animal selection request...');
    console.log('Context:', JSON.stringify(animalSelectionRequest.context, null, 2));
    
    const response = await axios.post(`${SERVER_URL}/open-ai-chat`, animalSelectionRequest);
    
    console.log('📥 Response status:', response.status);
    console.log('📥 Response data:', JSON.stringify(response.data, null, 2));
    
    if (response.data.deleted) {
      console.log('✅ Animal deletion via selection successful!');
      console.log(`   Deleted: ${response.data.animalData.name}`);
    } else if (response.data.error) {
      console.log('❌ Animal selection deletion failed:', response.data.message);
    } else {
      console.log('⚠️ Unexpected response format');
    }
  } catch (error) {
    console.error('❌ Animal selection request failed:', error.response?.data || error.message);
  }
}

// Run test
testAnimalSelectionForDeletion();
