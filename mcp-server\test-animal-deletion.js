const axios = require('axios');

const SERVER_URL = 'http://localhost:3001';

// Test data
const testUserId = 'test-user-123';
const testFarms = [
  {
    id: 'test-farm-123',
    name: 'Test Farm',
    ownerId: testUserId
  }
];

// Test cases for animal deletion
const testCases = [
  {
    name: 'Direct animal deletion with name',
    prompt: 'Delete my cow Daisy',
    expectedAction: 'delete_animal',
    expectedAnimalName: 'Daisy'
  },
  {
    name: 'Animal deletion with tag ID',
    prompt: 'Remove animal number 123',
    expectedAction: 'delete_animal',
    expectedAnimalTagId: '123'
  },
  {
    name: 'Animal sold',
    prompt: 'I sold the goat named <PERSON> yesterday',
    expectedAction: 'delete_animal',
    expectedReason: 'sold'
  },
  {
    name: 'Animal died',
    prompt: 'The cow died this morning',
    expectedAction: 'delete_animal',
    expectedReason: 'died'
  },
  {
    name: 'Generic animal deletion',
    prompt: 'I need to remove an animal from my list',
    expectedAction: 'delete_animal',
    expectedAnimalName: null
  },
  {
    name: 'Urdu animal deletion',
    prompt: 'جانور نمبر 45 مر گیا ہے',
    expectedAction: 'delete_animal',
    expectedReason: 'died'
  }
];

async function testAnimalDeletion() {
  console.log('🧪 Testing Animal Deletion Functionality...\n');

  for (const testCase of testCases) {
    console.log(`📋 Test: ${testCase.name}`);
    console.log(`💬 Prompt: "${testCase.prompt}"`);

    const testRequest = {
      prompt: testCase.prompt,
      userId: testUserId,
      language: testCase.prompt.includes('جانور') ? 'ur' : 'en',
      context: {},
      farms: testFarms
    };

    try {
      console.log('📤 Sending request...');
      
      const response = await axios.post(`${SERVER_URL}/open-ai-chat`, testRequest);
      
      console.log('📥 Response status:', response.status);
      console.log('📥 Response data:', JSON.stringify(response.data, null, 2));
      
      if (response.data.animalImages) {
        console.log('✅ Animal selection interface displayed!');
        console.log(`   Found ${response.data.animalImages.length} animals for selection`);
      } else if (response.data.deleted) {
        console.log('✅ Animal deletion successful!');
        console.log(`   Deleted: ${response.data.animalData.name}`);
      } else if (response.data.error) {
        console.log('❌ Animal deletion failed:', response.data.message);
      } else {
        console.log('⚠️ Unexpected response format');
      }
    } catch (error) {
      console.error('❌ Request failed:', error.response?.data || error.message);
    }

    console.log('─'.repeat(50));
  }
}

async function testAnimalSelectionForDeletion() {
  console.log('\n🧪 Testing Animal Selection for Deletion...\n');

  // Simulate clicking on an animal for deletion
  const animalSelectionRequest = {
    prompt: 'test-animal-id-123', // This would be the animal ID when user clicks
    userId: testUserId,
    language: 'en',
    context: {
      action: 'delete_animal',
      farmId: 'test-farm-123',
      farmName: 'Test Farm',
      reason: 'sold',
      needsAnimalSelection: true
    },
    farms: testFarms
  };

  try {
    console.log('📤 Sending animal selection request...');
    
    const response = await axios.post(`${SERVER_URL}/open-ai-chat`, animalSelectionRequest);
    
    console.log('📥 Response status:', response.status);
    console.log('📥 Response data:', JSON.stringify(response.data, null, 2));
    
    if (response.data.deleted) {
      console.log('✅ Animal deletion via selection successful!');
    } else if (response.data.error) {
      console.log('❌ Animal selection deletion failed:', response.data.message);
    } else {
      console.log('⚠️ Unexpected response format');
    }
  } catch (error) {
    console.error('❌ Animal selection request failed:', error.response?.data || error.message);
  }
}

// Run tests
async function runAllTests() {
  try {
    await testAnimalDeletion();
    await testAnimalSelectionForDeletion();
    console.log('\n✅ All animal deletion tests completed!');
  } catch (error) {
    console.error('❌ Test suite failed:', error);
  }
}

// Check if this script is being run directly
if (require.main === module) {
  runAllTests();
}

module.exports = {
  testAnimalDeletion,
  testAnimalSelectionForDeletion,
  runAllTests
};
