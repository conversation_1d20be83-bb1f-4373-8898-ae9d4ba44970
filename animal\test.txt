
    // ......................................................COW...........................................................................
    // SURGERY
    //GENERAL
    //Births
    // ......................................................GOAT...........................................................................
    // SURGERY
    "7aUOP8EPS5cVwLcwxriC": "انفیکشن کا علاج",
    "7aUOP8EPS5cVwLcwxriCDescription": "متاثرہ گانٹھوں کو دور کرنے کی چھوٹی سرجریز۔",
    "pvouErmOh3TVG43p9s6i": "خصی کرنا",
    "pvouErmOh3TVG43p9s6iDescription": "جدید ریوڑ کے انتظام کا عام عمل۔",
    //GENERAL
    "LzyeEwJexaf88IDgClz7": "پرزیٹ چیک",
    "LzyeEwJexaf88IDgClz7Description": "کیڑے کے لیے بصری + فضلہ کا معائنہ۔",
    "UNa9SD9RZ78YWd36CLyt": "ہووف ٹرم چیک",
    "UNa9SD9RZ78YWd36CLytDescription": "زیادہ بڑھنے اور پاؤں کے سڑنے سے بچاؤ۔",
    //Births
    "AsJ6SnhgWYsAGGDH5Bc5": "عام زچگی",
    "AsJ6SnhgWYsAGGDH5Bc5Description": "باقاعدہ، قدرتی پیدائش کا ریکارڈ۔",
    "XRY6bAyDbUVgDQpuJhXI": "مدد شدہ زچگی",
    "XRY6bAyDbUVgDQpuJhXIDescription": "دستی یا ویٹرنری مدد کی ضرورت ہوتی ہے۔",
    "zuPTuP95hIZohi2LsNyr": "مردہ پیدائش",
    "zuPTuP95hIZohi2LsNyrDescription": "ناکام پیدائش کا ریکارڈ۔",
    // ......................................................FISH...........................................................................


    // SURGERY
    "jy9eK07stymoKdzIfwoK": "نادر پروسیجرز",
    "jy9eK07stymoKdzIfwoKDescription": "عام طور پر مچھلی کی کاشت میں یہ عمل نہیں کیا جاتا۔",

    //GENERA
    "LYlCh4a7f7mZcu37rnXI": "پانی کے معیار کی جانچ",
    "LYlCh4a7f7mZcu37rnXIDescription": "پی ایچ، آکسیجن، امونیا کی سطحیں۔",
    "4kyHpnWSHlg5u8SnyIfI": "سلوک کی جانچ",
    "4kyHpnWSHlg5u8SnyIfIDescription": " بے قاعدہ تیرنا، بھوک میں کمی۔",

    //Birth
    "kPwkIu4AycoU467Icaik": "بچے نکلنے کی شرح",
    "kPwkIu4AycoU467IcaikDescription": "لاگ کریں کہ کتنے چھوٹے مچھلی کے بچے زندہ رہتے ہیں۔",
    "Qp3yZxglauwE1RsrXjGo": "انڈے دینے کا ریکارڈ",
    "Qp3yZxglauwE1RsrXjGoDescription": "نوٹ کریں کہ مچھلی کب انڈے دیتی ہے۔",



    // ......................................................POULTRY...........................................................................
    // Surgery
    "bLDSMAoBpR1LbHYn6yaQ": "نادر پروسیجرز",
    "bLDSMAoBpR1LbHYn6yaQDescription": "نادر پروسیجرز کے لیے استعمال ہوتا ہے۔",

    // Checkup
    "ZyITKHHo17WdW74SPiYD": "پَر چیک کرنا",
    "ZyITKHHo17WdW74SPiYDDescription": "پروں کا جھڑنا = تناؤ یا جوؤں کی علامت۔",
    "oFbLkXzGjzqmTyAZBp5C": "ٹانگوں کی طاقت کا معائنہ",
    "oFbLkXzGjzqmTyAZBp5CDescription": "خاص طور پر برائلر مرغیوں میں۔",

    // Birth
    "m079kjhlAXggGkvSjMHJ": "انڈے کی پیداوار",
    "m079kjhlAXggGkvSjMHJDescription": "انڈے کی پیداوار کو فی مرغی/بیچ کے لحاظ سے ٹریک کریں۔",
    "N5tY7cDdd9b6NZENolfc": "ہیٹچ رپورٹ",
    "N5tY7cDdd9b6NZENolfcDescription": "انکیوبیٹر یا قدرتی ہچنگ سے چوزوں کی تعداد",

/////////////////////
import { useState, useEffect } from 'react';
import { RecordType } from '@/types/record';
import { useLookupStore } from '@/store/lookup-store';

export const animalRecordOptions: AnimalRecordOptions = {
  'Cow': {
    [RecordType.MEDICATION]: [
      { 
        id: 'cow-med-antibiotics', 
        label: 'Antibiotics', 
        description: 'Used to treat bacterial infections such as mastitis, pneumonia.',
        imageUri: require('../assets/images/Cattle-stand-tallcowMed.jpg')
      },
      { 
        id: 'cow-med-anthelmintics', 
        label: 'Anthelmintics (Dewormers)', 
        description: 'For internal parasites like worms.',
        imageUri: require('../assets/images/abdc.png')
      },
      { 
        id: 'cow-med-pain-relievers', 
        label: 'Pain Relievers', 
        description: 'NSAIDs for inflammation or injury-related pain.',
        imageUri: require('../assets/images/melovemCowmed.png')
      },
      { 
        id: 'cow-med-digestive', 
        label: 'Digestive Boosters', 
        description: 'Improve rumen function and appetite.',
        imageUri: require('../assets/images/Aftovac Bottle-0.jpg')
      },
    ],
    [RecordType.VACCINATION]: [
      { 
        id: 'cow-vac-fmd', 
        label: 'FMD Vaccine', 
        description: 'Protects against Foot and Mouth Disease (every 6 months).',
        imageUri: require('../assets/images/Aftovac Bottle-0 (1).jpg')
      },
      { 
        id: 'cow-vac-hs', 
        label: 'HS Vaccine', 
        description: 'Prevents Hemorrhagic Septicemia (yearly).',
        imageUri: require('../assets/images/Intervac-HS-2.jpg')
      },
      { 
        id: 'cow-vac-bq', 
        label: 'BQ Vaccine', 
        description: 'Prevents Black Quarter (yearly).',
        imageUri: require('../assets/images/blackQuater.jpg')
      },
      { 
        id: 'cow-vac-brucellosis', 
        label: 'Brucellosis Vaccine', 
        description: 'For female calves to prevent infertility.',
        imageUri: require('../assets/images/RB-51-CZV.jpg')
      },
    ],
    [RecordType.SURGERY]: [],
    [RecordType.BIRTH]: [],
    [RecordType.GENERAL]: [],
  },
  'Goat': {
    [RecordType.MEDICATION]: [
      { 
        id: 'goat-med-coccidiostats', 
        label: 'Coccidiostats', 
        description: 'For prevention/treatment of coccidiosis.',
        imageUri: require('../assets/images/CoccidiostatsGoat.jpg')
      },
      { 
        id: 'goat-med-antibiotics', 
        label: 'Antibiotics', 
        description: 'For pneumonia, infections.',
        imageUri: require('../assets/images/Antibioticsgoat.jpg')
      },
      { 
        id: 'goat-med-dewormers', 
        label: 'Dewormers', 
        description: 'Every 3–6 months depending on region.',
        imageUri: require('../assets/images/Dewormersgoat.jpg')
      },
    ],
    [RecordType.VACCINATION]: [
      { 
        id: 'goat-vac-ppr', 
        label: 'PPR Vaccine', 
        description: 'Protects against Peste des Petits Ruminants.',
        imageUri:'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcStZ-WhDwt03d6GilETz-gPGLXK9O7k-1Znjw&s'
      },
      { 
        id: 'goat-vac-enterotoxemia', 
        label: 'Enterotoxemia Vaccine', 
        description: 'Prevents overeating disease.',
        imageUri:'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTm_Eg_nDDNOhKDD7AFLE7X494hZ8S_VTTvdw&s'
      },
      { 
        id: 'goat-vac-fmd-pox', 
        label: 'FMD / Goat Pox Vaccines', 
        description: 'Protect from outbreaks.',
        imageUri:'https://www.merck-animal-health-usa.com/wp-content/uploads/sites/54/2022/12/PP-VAC_product-vial.png'
      },
    ],
    [RecordType.SURGERY]: [],
    [RecordType.BIRTH]: [],
    [RecordType.GENERAL]: [],
  },
  'Fish': {
    [RecordType.MEDICATION]: [
      { 
        id: 'fish-med-antibacterials', 
        label: 'Antibacterials', 
        description: 'For waterborne bacterial infections.',
        imageUri: require('../assets/images/AntibacterialsFish.jpg')
      },
      { 
        id: 'fish-med-antifungals', 
        label: 'Antifungals', 
        description: 'Used in fungal outbreaks (white patches).',
        imageUri: require('../assets/images/Antifungalsfish.jpg')
      },
      { 
        id: 'fish-med-water', 
        label: 'Water Conditioners', 
        description: 'Improve tank/pond health.',
        imageUri: require('../assets/images/WaterConditionersFish.jpg')
      },
    ],
    [RecordType.VACCINATION]: [
      { 
        id: 'fish-vac-bacterial', 
        label: 'Bacterial Disease Vaccines', 
        description: 'For Aeromonas, Vibrio (mostly in commercial fish).'
      },
      { 
        id: 'fish-vac-ipn', 
        label: 'IPN Vaccine', 
        description: 'Infectious Pancreatic Necrosis vaccine.'
      },
    ],
    [RecordType.SURGERY]: [],
    [RecordType.BIRTH]: [],
    [RecordType.GENERAL]: [],
  },
  'Poultry': {
    [RecordType.MEDICATION]: [
      { 
        id: 'poultry-med-coccidiostats', 
        label: 'Coccidiostats', 
        description: 'Prevent deadly coccidiosis.',
        imageUri: require('../assets/images/CoccidiostatsPoultry.jpg')
      },
      { 
        id: 'poultry-med-antibiotics', 
        label: 'Antibiotics', 
        description: 'For respiratory or digestive infections.',
        imageUri: require('../assets/images/AntibioticsPoultry.jpg')
      },
      { 
        id: 'poultry-med-vitamins', 
        label: 'Vitamin Supplements', 
        description: 'Boost immunity, especially in broilers.',
        imageUri: require('../assets/images/VitaminSupplements.jpg')
      },
    ],
    [RecordType.VACCINATION]: [
      { 
        id: 'poultry-vac-newcastle', 
        label: 'Newcastle Disease', 
        description: 'One of the most critical.'
      },
      { 
        id: 'poultry-vac-gumboro', 
        label: 'Gumboro (IBD) Vaccine', 
        description: 'For chicks at 2–3 weeks.'
      },
      { 
        id: 'poultry-vac-mareks', 
        label: 'Marek\'s Disease', 
        description: 'Given to chicks at hatch.'
      },
    ],
    [RecordType.SURGERY]: [],
    [RecordType.BIRTH]: [],
    [RecordType.GENERAL]: [],
  },
};


/////////////HOOK

export function useRecordOptions(selectedSpecies :any, recordType:any) {
    const { getLookupsByCategory } = useLookupStore();
  const [records, setRecords] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const loadRecords = async () => {
      if (!selectedSpecies || !recordType) {
        setRecords([]);
        return;
      }

      // For static records (MEDICATION & VACCINATION)
      if (recordType === RecordType.MEDICATION || recordType === RecordType.VACCINATION) {
        const staticRecords = getRecordOptions(selectedSpecies, recordType);
        setRecords(staticRecords);
        return;
      }

      // For dynamic records (SURGERY, BIRTH, GENERAL)
      setLoading(true);
      try {
        // Map record types to Firebase categories
        let categoryName;
        switch (recordType) {
          case RecordType.BIRTH:
            categoryName = "births";
            break;
          case RecordType.SURGERY:
            categoryName = "surgery";
            break;
          case RecordType.GENERAL:
            categoryName = "checkup";
            break;
          default:
            setRecords([]);
            setLoading(false);
            return;
        }
        
        // Fetch from Firebase
        const firebaseRecords = await getLookupsByCategory(categoryName);
        
        // Filter records for this specific animal
        const animalSpecificRecords = firebaseRecords
          .filter(record => {
            const titleWords = record.title.split(' ');
            const lastWord = titleWords[titleWords.length - 1];
            return lastWord === selectedSpecies;
          })
          .map(record => ({
            id: record.id,
            label: record.title.replace(` ${selectedSpecies}`, ''),
            description: record.description
          }));
        
        setRecords(animalSpecificRecords);
        
      } catch (error) {
        console.error('Error loading records:', error);
        setRecords([]);
      } finally {
        setLoading(false);
      }
    };

    loadRecords();
  }, [selectedSpecies, recordType]);

  return { records, loading };
}

///////////////////////adddddddddd
import { getRecordOptions, useRecordOptions } from '@/constants/recordOptions';

  // Memoize record options based on selected animal and record type
  // const recordOptions = useMemo(() => getRecordOptions(selectedSpecies, recordType), [selectedSpecies, recordType]);


  const { records: recordOptions, loading } = useRecordOptions(selectedSpecies, recordType);

  console.log("Sherrry", recordOptions)

  // const recordOptionItems :any =[]
  const recordOptionItems = useMemo(() => {
    return recordOptions.map(option => ({
      id: option.id,
      label: language === 'ur' ? t(`records.${option.id}`) || option.label : option.label,
      description: language === 'ur' ? t(`records.${option.id}Description`) || option.description : option.description,
      // imageUri: option.imageUri ? (
      //   typeof option.imageUri === 'string'
      //     ? { uri: option.imageUri }
      //     : option.imageUri
      // ) : undefined
    }));
  }, [recordOptions, language, t]);

............ADD FARMmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Image,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { useRouter, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useThemeColors } from '@/hooks/useThemeColors'; // Import the theme hook
import { useAuthStore } from '@/store/auth-store';
import { useFarmStore } from '@/store/farm-store';
import { useLookupStore } from '@/store/lookup-store';

import { useTranslation } from '@/hooks/useTranslation';
import { Save, MapPin, Building2, Camera, Mic, MicOff, Ruler, FileText } from 'lucide-react-native';
import Input from '@/components/Input';
import Button from '@/components/Button';
import { useToast } from '@/contexts/ToastContext';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { firestore } from '@/config/firebase';
import * as ImagePicker from 'expo-image-picker';
import ImageCaptureButtons from '@/components/ImageCaptureButtons';
import GenericDropdown from '@/components/GenericDropdown';
import { startSpeechRecognition, stopSpeechRecognition, setCurrentField } from '@/services/speech-service';
import { getStoredLanguage, getSpeechLanguageCode } from '@/services/language-service';
import { useAudioFeedback } from '@/hooks/useAudioFeedback';
import { uploadImage } from '@/services/storage-service';
export default function AddFarmScreen() {
  const { t, language } = useTranslation();
  const router = useRouter();
  const { user } = useAuthStore();
  const { addFarm, isLoading } = useFarmStore();
  const { fetchLookups, getLookupsByCategoryParsedData } = useLookupStore();
  const { showToast } = useToast();
  const themedColors = useThemeColors(); // Use the theme hook

  // Immediate check for caretaker role - TRIPLE PROTECTION
  if (!user || !(user.role === 'owner' || user.role === 'admin')) {
    console.log('AddFarmScreen - Unauthorized role detected:', user?.role);
    // Use setTimeout to ensure this runs after component mount
    setTimeout(() => {
      router.replace('/(tabs)');
      Alert.alert(t('common.permissionDenied'), t('common.noAccessToThisFeature'));
    }, 0);
    // Return empty component while redirecting
    return null;
  }

  const styles = getStyles(themedColors, language); // Generate styles with themed colors
  const { playFeedback } = useAudioFeedback();

  const [name, setName] = useState('');
  const [location, setLocation] = useState('');
  const [status, setStatus] = useState<string | undefined>();
  const [photoURL, setPhotoURL] = useState('');
  const [farmType, setFarmType] = useState<string | undefined>();
  const [size, setSize] = useState('');
  const [sizeUnit, setSizeUnit] = useState('acre');
  const [description, setDescription] = useState('');
  const [isRecordingDescription, setIsRecordingDescription] = useState(false);
  const [isProcessingSpeech, setIsProcessingSpeech] = useState(false);
  const [errors, setErrors] = useState({
    name: '',
    location: '',
    size: '',
  });

  const farmTypes = getLookupsByCategoryParsedData('farmType', 'farms.farmType.');
  const farmStatuses = getLookupsByCategoryParsedData('farmStatus', 'farms.farmStatus.');
console.log("ssss", farmTypes)
  // Fetch lookups on component mount
  useEffect(() => {
    fetchLookups();
  }, [fetchLookups]);

  // Set default farm type when lookups are loaded
  useEffect(() => {
    // Set default only if it hasn't been set by the user and data is available
    if (farmType === undefined && farmTypes.length > 0) {
      // Find by ID ('livestock') for robustness, as labels can be translated.
      const defaultType = farmTypes.find(item => item.id === 'livestock') || farmTypes[0];
      if (defaultType) {
        setFarmType(defaultType.id);
      }
    }
  }, [farmTypes, farmType]);

  // Set default status when lookups are loaded
  useEffect(() => {
    if (status === undefined && farmStatuses.length > 0) {
      // Set 'active' as the default status. Finding by ID is best for multi-language support.
      const defaultStatus = farmStatuses.find(item => item.label.toLowerCase() === 'active') || farmStatuses[0];
      setStatus(defaultStatus.id);
    }
  }, [farmStatuses, status]);

  // Photo handling functions
  const handleTakePhoto = async () => {
    try {
      const permissionResult = await ImagePicker.requestCameraPermissionsAsync();
      if (!permissionResult.granted) {
        Alert.alert(t('common.permissionRequired'), t('common.cameraPermission'));
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setPhotoURL(result.assets[0].uri);
      }
    } catch (error) {
      Alert.alert(t('common.error'), t('common.photoError'));
    }
  };

  const handlePickImage = async () => {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (!permissionResult.granted) {
        Alert.alert(t('common.permissionRequired'), t('common.galleryPermission'));
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setPhotoURL(result.assets[0].uri);
      }
    } catch (error) {
      Alert.alert(t('common.error'), t('common.photoError'));
    }
  };

  // Voice recording for description
  const startRecordingDescription = async () => {
    try {
      setIsRecordingDescription(true);
      setIsProcessingSpeech(true);

      const appLanguage = await getStoredLanguage();
      const speechLanguage = getSpeechLanguageCode(appLanguage);
      setCurrentField('description');
      await startSpeechRecognition({ language: speechLanguage });
      playFeedback('toggle');
    } catch (error) {
      console.error('Error starting speech recognition:', error);
      Alert.alert(t('common.error'), t('common.speechRecognitionError'));
      setIsProcessingSpeech(false);
      setIsRecordingDescription(false);
      setCurrentField(null);
    }
  };

  const stopRecordingDescription = async () => {
    try {
      const transcription = await stopSpeechRecognition();
      if (transcription && transcription.trim().length > 0) {
        setDescription(prev => prev ? `${prev} ${transcription}` : transcription);
      }
      playFeedback('toggle');
    } catch (error) {
      console.error('Error stopping speech recognition:', error);
      Alert.alert(t('common.error'), t('common.speechRecognitionError'));
    } finally {
      setIsRecordingDescription(false);
      setIsProcessingSpeech(false);
      setCurrentField(null);
    }
  };

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      name: '',
      location: '',
      size: '',
    };

    if (!name.trim()) {
      newErrors.name = t('farms.nameRequired');
      isValid = false;
    }

    if (!location.trim()) {
      newErrors.location = t('farms.locationRequired');
      isValid = false;
    }

    if (size && isNaN(Number(size))) {
      newErrors.size = t('farms.sizeInvalid');
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  // Add this function to get current lookup IDs
  const getCurrentLookupIds = () => {
    const farmTypes = getLookupsByCategoryParsedData('farmType', 'farms.farmType.');
    const farmStatuses = getLookupsByCategoryParsedData('farmStatus', 'farms.farmStatus.');
    const sizeUnits = getLookupsByCategoryParsedData('areaUnit', 'farms.areaUnit.');

    return {
      currentFarmTypeId: farmType || (farmTypes.find(item => item.id === 'livestock')?.id || farmTypes[0]?.id),
      currentStatusId: status || (farmStatuses.find(item => item.label.toLowerCase() === 'active')?.id || farmStatuses[0]?.id),
      currentSizeUnitId: sizeUnit || (sizeUnits.find(item => item.label.toLowerCase() === 'acre')?.id || sizeUnits[0]?.id)
    };
  };

  const handleSave = async () => {
    if (!validateForm()) return;
    if (!user) return;

    // Get current lookup IDs for reference
    const currentIds = getCurrentLookupIds();
    console.log('Current Lookup IDs:', currentIds);

    try {
      // Upload photo to Firebase Storage if available
      let uploadedPhotoURL = '';
      if (photoURL) {
        try {
          const timestamp = Date.now();
          const filename = `farms/${user.id}/${timestamp}_${Math.random().toString(36).substring(7)}.jpg`;
          uploadedPhotoURL = await uploadImage(photoURL, filename);
        } catch (uploadError: any) {
          showToast({
            type: 'error',
            title: t('common.error'),
            message: uploadError.message || t('validation.photoUploadError'),
          });
          return;
        }
      }

      // Add the farm to Firestore with current lookup IDs
      const newFarm = await addFarm({
        name,
        location: {
          address: location,
          latitude: 0, // TODO: Add GPS functionality
          longitude: 0, // TODO: Add GPS functionality
        },
        status, //: currentIds.currentStatusId, // Use dynamic ID
        ownerId: user.id,
        photoURL: uploadedPhotoURL,
        size: size ? Number(size) : undefined,
        sizeUnit: currentIds.currentSizeUnitId, // Use dynamic ID
        type: currentIds.currentFarmTypeId, // Use dynamic ID
        description,
      });
      // Update the owner's assignedFarmIds array
      try {
        const userRef = doc(firestore, 'users', user.id);
        const userDoc = await getDoc(userRef);

        if (userDoc.exists()) {
          const userData = userDoc.data();
          const assignedFarmIds = userData.assignedFarmIds || [];

          // Add the new farm ID to the assignedFarmIds array if it's not already there
          if (!assignedFarmIds.includes(newFarm.id)) {
            await updateDoc(userRef, {
              assignedFarmIds: [...assignedFarmIds, newFarm.id]
            });
            console.log(`Updated user ${user.id} with new farm ${newFarm.id}`);
          }
        }
      } catch (userUpdateError) {
        console.error('Error updating user assignedFarmIds:', userUpdateError);
        // Continue even if user update fails
      }
  
      showToast({
        type: 'success',
        title: t('common.success'),
        message: t('farms.addSuccess'),
      });
  
      // Navigate to the farm detail screen
      router.replace(`/farms/${newFarm.id}`);
    } catch (error) {
      console.error('Error adding farm:', error);
      showToast({
        type: 'error',
        title: t('common.error'),
        message: t('farms.addError'),
      });
    }
  };
  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: t('farms.addFarm'),
          headerBackTitle: t('common.back'),
        }}
      />
      <ScrollView style={styles.scrollView}>
        <View style={styles.formContainer}>
          {/* Photo Upload */}
          <View style={styles.formGroup}>
            {photoURL ? (
              <View style={styles.imageContainer}>
                <Image source={{ uri: photoURL }} style={styles.farmImage} />
                <TouchableOpacity
                  style={styles.removeImageButton}
                  onPress={() => setPhotoURL('')}
                >
                  <Text style={styles.removeImageText}>×</Text>
                </TouchableOpacity>
              </View>
            ) : (
              <View style={styles.imageUploadCard}>
                <View style={styles.imagePlaceholder}>
                  <Camera size={40} color={themedColors.textSecondary} />
                  <Text style={styles.imagePlaceholderText}>{t('common.addPhoto')}</Text>
                </View>
                <ImageCaptureButtons
                  onTakePhoto={handleTakePhoto}
                  onChooseFromLibrary={handlePickImage}
                />
              </View>
            )}
          </View>

          {/* Farm Name */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.name')}*</Text>
            </View>
            <Input
              value={name}
              onChangeText={setName}
              placeholder={t('farms.namePlaceholder')}
              error={errors.name} // @ts-ignore
              leftIcon={<Building2 size={20} color={themedColors.textSecondary} />}
              style={language === 'ur' ? styles.urduText : null}
            />
          </View>

          {/* Farm Type */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.type')}</Text>
            </View>
            <GenericDropdown
              placeholder={t('farms.selectType')}
              items={farmTypes}
              value={farmType}
              onSelect={setFarmType}
              modalTitle={t('farms.selectType')}
              searchPlaceholder={t('farms.searchTypes')}
              disabled={false}
            />
          </View>

          {/* Size */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.size')}</Text>
            </View>
            <Input
              value={size}
              onChangeText={setSize}
              placeholder={t('farms.sizePlaceholder')}
              error={errors.size}
              leftIcon={<Ruler size={20} color={themedColors.textSecondary} />}
              keyboardType="numeric"
              style={language === 'ur' ? styles.urduText : null}
            />
          </View>

          {/* Size Unit */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.sizeUnit')}</Text>
            </View>
            <GenericDropdown
              placeholder={t('farms.selectUnit')}
              items={getLookupsByCategoryParsedData('areaUnit', 'farms.areaUnit.')}
              value={sizeUnit}
              onSelect={setSizeUnit}
              modalTitle={t('farms.selectUnit')}
              searchPlaceholder={t('farms.searchUnits')}
            />
          </View>

          {/* Location */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.location')}*</Text>
            </View>
            <Input
              value={location}
              onChangeText={setLocation}
              placeholder={t('farms.locationPlaceholder')}
              error={errors.location} // @ts-ignore
              leftIcon={<MapPin size={20} color={themedColors.textSecondary} />}
              style={language === 'ur' ? styles.urduText : null}
            />
          </View>

          {/* Description with Voice */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <FileText size={24} color={themedColors.primary} />
              <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>
                {t('common.description')}
              </Text>
              {Platform.OS !== 'web' && (
                <TouchableOpacity
                  style={[
                    styles.voiceButtonHeader,
                    isRecordingDescription && styles.voiceButtonRecording
                  ]}
                  onPress={() => {
                    if (isRecordingDescription) {
                      stopRecordingDescription();
                    } else {
                      startRecordingDescription();
                    }
                  }}
                >
                  {isRecordingDescription ? (
                    <MicOff size={20} color={themedColors.error} />
                  ) : (
                    <Mic size={20} color={themedColors.primary} />
                  )}
                </TouchableOpacity>
              )}
            </View>
            <Input
              value={description}
              onChangeText={setDescription}
              placeholder={t('farms.descriptionPlaceholder')}
              multiline
              numberOfLines={4}
              style={[
                styles.textArea,
                language === 'ur' ? styles.urduText : null
              ]}
            />
            {isProcessingSpeech && (
              <View style={styles.speechProcessing}>
                <ActivityIndicator size="small" color={themedColors.primary} />
                <Text style={styles.speechProcessingText}>
                  {t('common.processingSpeech')}
                </Text>
              </View>
            )}
          </View>

          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{t('farms.status')}</Text>
            </View>
            <View style={[
              styles.statusButtons,
              language === 'ur' && { flexDirection: 'row-reverse' }
            ]}>
              {farmStatuses.map((statusItem) => (
                <TouchableOpacity
                  key={statusItem.id}
                  style={[
                    styles.statusButton,
                    status === statusItem.id && styles.statusButtonSelected
                  ]}
                  onPress={() => setStatus(statusItem.id)}
                >
                  <Text style={[
                    styles.statusButtonText,
                    status === statusItem.id && styles.statusButtonTextSelected,
                    language === 'ur' ? styles.urduText : null
                  ]}>
                    {statusItem.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.buttonContainer}>
            <Button
              title={t('farms.saveFarm')}
              onPress={handleSave}
              isLoading={isLoading}
              leftIcon={<Save size={20} color="white" />}
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  scrollView: {
    flex: 1,
  },
  formContainer: {
    padding: 16,
    paddingTop: 24, // Add some top padding for better spacing
  },
  formGroup: {
    marginBottom: 10, // Increased margin for better separation
  },
  labelContainer: {
    marginBottom: 8,
    flexDirection: language === 'ur' ? 'row-reverse' : 'row', // Adjust for Urdu
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: themedColors.text,
    textAlign: language === 'ur' ? 'right' : 'left', // Adjust for Urdu
  },
  statusButtons: {
    flexDirection: 'row',
    justifyContent: language === 'ur' ? 'flex-end' : 'flex-start', // Adjust for Urdu
    flexWrap: 'wrap',
  },
  statusButton: {
    backgroundColor: themedColors.card,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: language === 'ur' ? 0 : 8, // Adjust for Urdu
    marginLeft: language === 'ur' ? 8 : 0, // Adjust for Urdu
    marginBottom: 8,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  statusButtonSelected: {
    backgroundColor: themedColors.primary,
    borderColor: themedColors.primary,
  },
  statusButtonText: {
    color: themedColors.text,
    fontSize: 14,
    fontWeight: '500',
  },
  statusButtonTextSelected: {
    color: 'white',
  },
  buttonContainer: {
    marginTop: 32, // Increased margin for the button
  },
  // Image upload styles
  imageContainer: {
    position: 'relative',
    marginBottom: 16,

    
  },
  farmImage: {
    width: '100%',
    height: 200,
    borderRadius: 12,
    backgroundColor: themedColors.card,
  },
  removeImageButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: themedColors.error,
    borderRadius: 16,
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeImageText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  imageUploadCard: {
    backgroundColor: themedColors.card,
    borderRadius: 12,
    padding: 5,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: themedColors.border,
    borderStyle: 'dashed',
  },
  imagePlaceholder: {
    alignItems: 'center',
    marginBottom: 16,
  },
  imagePlaceholderText: {
    color: themedColors.textSecondary,
    fontSize: 16,
    marginTop: 8,
    textAlign: 'center',
  },
  // Voice recording styles
  voiceButtonHeader: {
    backgroundColor: themedColors.card,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: language === 'ur' ? 0 : 8,
    marginRight: language === 'ur' ? 8 : 0,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  voiceButtonRecording: {
    backgroundColor: themedColors.error + '20',
    borderColor: themedColors.error,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  speechProcessing: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    paddingHorizontal: 12,
  },
  speechProcessingText: {
    color: themedColors.textSecondary,
    fontSize: 14,
    marginLeft: 8,
  },
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
  },
});


////////////////////////Penancy 
Add Pregnancy Functionality

If the user types a message requesting to "add pregnancy", trigger the pregnancy addition flow.

The farm used should be the same one mentioned in the chat header.

The user will mention the male and female animal names in their message. Use OpenAI to extract those names from the text.

Then, search for both animals in the animal list of the selected farm.

Logic:
If both male and female animal names are found: Proceed to add a natural pregnancy record.

If only the female animal name is found: Consider it an Artificial Insemination case.

If either or both animals are not found in the selected farm:

Fetch the list of animals with pictures and prompt the user to select the missing animal(s).

If both animals are missing:

First, show a list of female animals for the user to select.

Once a female animal is selected, show a list of male animals.

If only one animal is missing:

Show only the list for the missing one (either male or female) for selection.

IF pregnnacy status is not mentioned than it should be Prengant by by default