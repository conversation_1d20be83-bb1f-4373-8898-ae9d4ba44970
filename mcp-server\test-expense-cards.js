const axios = require('axios');

const SERVER_URL = 'http://localhost:3001';

// Test data
const testUserId = 'test-user-123';
const testFarms = [
  {
    id: '7owMeeTfGSYqaRpgIsOE', // Use real farm ID from your logs
    name: 'Haven View.',
    ownerId: testUserId
  }
];

async function testExpenseCardDisplay() {
  console.log('🧪 Testing Expense Card Display...\n');

  try {
    console.log('📤 Triggering expense deletion to see card format...');
    
    const response = await axios.post(`${SERVER_URL}/open-ai-chat`, {
      prompt: 'Delete expense',
      userId: testUserId,
      language: 'en',
      farms: testFarms
    });

    console.log('📥 Response status:', response.status);
    console.log('📥 Response data structure:');
    console.log('  - message:', response.data.message ? 'Present' : 'Missing');
    console.log('  - expenseImages:', response.data.expenseImages ? `Array with ${response.data.expenseImages.length} items` : 'Missing');
    console.log('  - context:', response.data.context ? 'Present' : 'Missing');
    console.log('  - error:', response.data.error ? 'True' : 'False');

    if (response.data.expenseImages && response.data.expenseImages.length > 0) {
      console.log('\n✅ Expense cards received! Card format:');
      response.data.expenseImages.forEach((expense, index) => {
        console.log(`\n📋 Card ${index + 1}:`);
        console.log(`  - ID: ${expense.id}`);
        console.log(`  - Name: ${expense.name}`);
        console.log(`  - Amount: ${expense.amount}`);
        console.log(`  - Date: ${expense.date}`);
        console.log(`  - Payment Method: ${expense.paymentMethod}`);
        console.log(`  - Description: ${expense.description}`);
        console.log(`  - Has Image: ${expense.imageUri ? 'Yes' : 'No'}`);
      });

      // Test clicking on the first expense
      if (response.data.expenseImages.length > 0) {
        const firstExpense = response.data.expenseImages[0];
        console.log(`\n📤 Testing expense selection: ${firstExpense.name}`);
        
        const selectionResponse = await axios.post(`${SERVER_URL}/open-ai-chat`, {
          prompt: firstExpense.id,
          userId: testUserId,
          language: 'en',
          context: response.data.context,
          farms: testFarms
        });

        console.log('📥 Selection response status:', selectionResponse.status);
        
        if (selectionResponse.data.deleted) {
          console.log('✅ Expense deletion successful!');
          console.log(`   Deleted: ${selectionResponse.data.expenseData.category}`);
          console.log(`   Amount: ${selectionResponse.data.expenseData.currency} ${selectionResponse.data.expenseData.amount}`);
        } else if (selectionResponse.data.error) {
          console.log('⚠️ Expected error (expense might not exist in test environment)');
          console.log(`   Message: ${selectionResponse.data.message}`);
        } else {
          console.log('❓ Unexpected response format');
          console.log('   Response:', JSON.stringify(selectionResponse.data, null, 2));
        }
      }

    } else if (response.data.message?.includes('No expenses found')) {
      console.log('✅ Expected behavior - no expenses in farm');
      console.log(`   Message: ${response.data.message}`);
    } else {
      console.log('❌ Unexpected response format');
      console.log('   Full response:', JSON.stringify(response.data, null, 2));
    }

  } catch (error) {
    console.error('❌ Error testing expense cards:', error.response?.data || error.message);
  }
}

async function testExpenseCardFormats() {
  console.log('\n🧪 Testing Different Expense Deletion Commands...\n');

  const commands = [
    'Delete expense',
    'Remove expense', 
    'Remove exp',
    'Vanish expense',
    'خرچہ حذف کریں'
  ];

  for (const command of commands) {
    try {
      console.log(`📤 Testing: "${command}"`);
      
      const response = await axios.post(`${SERVER_URL}/open-ai-chat`, {
        prompt: command,
        userId: testUserId,
        language: command.includes('خرچہ') ? 'ur' : 'en',
        farms: testFarms
      });

      if (response.data.expenseImages) {
        console.log(`✅ Card format working - ${response.data.expenseImages.length} expenses`);
      } else if (response.data.message?.includes('No expenses found') || response.data.message?.includes('اخراجات نہیں ملے')) {
        console.log('✅ No expenses message (expected)');
      } else {
        console.log('❌ Unexpected response');
        console.log(`   Message: ${response.data.message?.substring(0, 100)}...`);
      }
      
    } catch (error) {
      console.error(`❌ Error with "${command}":`, error.response?.data?.message || error.message);
    }
  }
}

// Run tests
async function runTests() {
  await testExpenseCardDisplay();
  console.log('\n' + '='.repeat(60) + '\n');
  await testExpenseCardFormats();
}

runTests();
