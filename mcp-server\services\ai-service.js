const { OpenAI } = require('openai');

// Initialize OpenAI client automatically with environment variable
let openai = null;

const initializeOpenAI = (apiKey) => {
  const key = apiKey || process.env.OPENAI_API_KEY;

  if (!key) {
    console.error('OpenAI API key is required');
    return false;
  }

  openai = new OpenAI({
    apiKey: key,
  });

  console.log('OpenAI client initialized successfully');
  return true;
};

// Auto-initialize if environment variable is available
if (process.env.OPENAI_API_KEY && !openai) {
  try {
    initializeOpenAI();
  } catch (error) {
    console.log('Auto-initialization failed, will try again when called');
  }
}

const analyzePromptWithAI = async (prompt, context = 'general') => {
  // Try to initialize if not already done
  if (!openai) {
    const success = initializeOpenAI();
    if (!success) {
      throw new Error('OpenAI client not initialized - API key missing');
    }
  }

  let systemMessage = '';
  
  if (context === 'farm') {
    systemMessage = `You are an expert at extracting farm information from text prompts.
    Analyze the user's message and extract farm-related information.
    
    Return ONLY a JSON object with the following structure:
    {
      "farmName": "exact farm name as written by user or null",
      "farmLocation": "exact address/location as written or null", 
      "farmSize": "exact size number as written or null",
      "sizeUnit": "exact unit as written (acre/hectare/marla/kanal/etc) or null",
      "farmType": "exact farm type as written (livestock/dairy/crop/poultry/mixed/etc) or null",
      "action": "save|add|update|change_name|change_location|change_address|change_type|change_size|analyze|other",
      "newName": "new name if user wants to change name or null",
      "newLocation": "new location if user wants to change address or null",
      "confidence": "confidence level 1-100"
    }
    
    Examples:
    - "Add this farm, Farm name is Green Valley, its 150 acre and address is Rural Road and type is dairy" → {"farmName": "Green Valley", "farmLocation": "Rural Road", "farmSize": "150", "sizeUnit": "acre", "farmType": "dairy", "action": "add", "confidence": 95}
    - "Change name to Sunny Farm" → {"action": "change_name", "newName": "Sunny Farm", "confidence": 100}
    - "Change address to Main Street" → {"action": "change_location", "newLocation": "Main Street", "confidence": 100}
    - "Change type to dairy" → {"action": "change_type", "farmType": "dairy", "confidence": 100}
    - "Change size to 150 acres" → {"action": "change_size", "farmSize": "150", "sizeUnit": "acres", "confidence": 100}`;
  } else if (context === 'farm_delete') {
    systemMessage = `You are an expert at analyzing farm deletion requests. Your primary task is to distinguish between farm DELETION and farm CREATION commands.

    CRITICAL: Only identify as deletion if the user explicitly uses deletion verbs (delete, remove, demolish, destroy, eliminate) with farm references.

    Return ONLY a JSON object with this structure:
    {
      "action": "delete|remove|demolish|destroy|eliminate|other",
      "farmName": "exact farm name mentioned by user or null",
      "confidence": "confidence level 1-100",
      "isDeletion": "true if this is clearly a deletion request, false otherwise"
    }

    DELETION Examples (isDeletion: true):
    - "Delete Haven View Farm" → {"action": "delete", "farmName": "Haven View Farm", "confidence": 100, "isDeletion": true}
    - "Remove Green Valley Farm" → {"action": "remove", "farmName": "Green Valley Farm", "confidence": 100, "isDeletion": true}
    - "Demolish the Sunny Farm" → {"action": "demolish", "farmName": "Sunny Farm", "confidence": 100, "isDeletion": true}
    - "Delete the farm" → {"action": "delete", "farmName": null, "confidence": 95, "isDeletion": true}
    - "Remove farm" → {"action": "remove", "farmName": null, "confidence": 95, "isDeletion": true}
    - "Destroy this farm" → {"action": "destroy", "farmName": null, "confidence": 95, "isDeletion": true}
    - "حذف کریں Haven View Farm" → {"action": "delete", "farmName": "Haven View Farm", "confidence": 100, "isDeletion": true}
    - "فارم کو ہٹا دیں" → {"action": "remove", "farmName": null, "confidence": 95, "isDeletion": true}
    - "منہدم کریں" → {"action": "demolish", "farmName": null, "confidence": 95, "isDeletion": true}

    NOT DELETION Examples (isDeletion: false):
    - "Create a new farm" → {"action": "other", "farmName": null, "confidence": 100, "isDeletion": false}
    - "Add farm called Green Valley" → {"action": "other", "farmName": "Green Valley", "confidence": 100, "isDeletion": false}
    - "Farm name is Sunny Farm" → {"action": "other", "farmName": "Sunny Farm", "confidence": 100, "isDeletion": false}
    - "Save this farm" → {"action": "other", "farmName": null, "confidence": 100, "isDeletion": false}
    - "what is weather" → {"action": "other", "farmName": null, "confidence": 100, "isDeletion": false}`;
  } else if (context === 'save_intent') {
    systemMessage = `You are an expert at analyzing user intent for save/confirmation actions.
    Analyze if the user wants to save/confirm/proceed with a previously shown farm analysis.

    Return ONLY a JSON object:
    {
      "action": "save|confirm|change|other",
      "confidence": "confidence level 1-100"
    }

    Examples:
    - "save" → {"action": "save", "confidence": 100}
    - "yes" → {"action": "confirm", "confidence": 95}
    - "ok" → {"action": "confirm", "confidence": 95}
    - "okay" → {"action": "confirm", "confidence": 95}
    - "go on" → {"action": "confirm", "confidence": 90}
    - "add it" → {"action": "save", "confidence": 95}
    - "proceed" → {"action": "confirm", "confidence": 95}
    - "confirm" → {"action": "confirm", "confidence": 100}
    - "do it" → {"action": "confirm", "confidence": 90}
    - "let's go" → {"action": "confirm", "confidence": 85}
    - "ہاں" → {"action": "confirm", "confidence": 95}
    - "اوکے" → {"action": "confirm", "confidence": 95}
    - "ٹھیک ہے" → {"action": "confirm", "confidence": 95}
    - "شامل کرو" → {"action": "save", "confidence": 95}
    - "محفوظ کرو" → {"action": "save", "confidence": 100}
    - "change name to John" → {"action": "change", "confidence": 100}
    - "what is weather" → {"action": "other", "confidence": 100}`;
  } else if (context === 'animal') {
    systemMessage = `You are an expert at extracting animal information from text prompts.
    Analyze the user's message and extract animal-related information.

    Return ONLY a JSON object with the following structure:
    {
      "animalName": "exact animal name as written by user or null",
      "species": "exact species as written (cow/buffalo/goat/sheep/chicken/etc) or null",
      "breed": "exact breed as written or null",
      "age": "exact age number as written or null",
      "weight": "exact weight number as written or null",
      "gender": "male|female|null",
      "action": "save|add|update|change_name|analyze|other",
      "newName": "new name if user wants to change name or null",
      "confidence": "confidence level 1-100"
    }

    Examples:
    - "Animal Name: Browni" → {"animalName": "Browni", "action": "add", "confidence": 95}
    - "Add cow named Bella" → {"animalName": "Bella", "species": "cow", "action": "add", "confidence": 95}
    - "Buffalo name is Moti, age 5 years" → {"animalName": "Moti", "species": "buffalo", "age": "5", "action": "add", "confidence": 95}
    - "Change name to Blacky" → {"action": "change_name", "newName": "Blacky", "confidence": 100}
    - "Female goat weighs 45 kg" → {"species": "goat", "gender": "female", "weight": "45", "action": "add", "confidence": 90}`;
  } else if (context === 'animal_action') {
    systemMessage = `You are an expert at analyzing user intent for animal-related actions.
    Analyze if the user wants to save/confirm/change an animal's information.

    Return ONLY a JSON object:
    {
      "action": "save|confirm|change_name|other",
      "newName": "new name if changing name or null",
      "confidence": "confidence level 1-100"
    }

    Examples:
    - "save" → {"action": "save", "confidence": 100}
    - "yes" → {"action": "confirm", "confidence": 95}
    - "change name to Blacky" → {"action": "change_name", "newName": "Blacky", "confidence": 100}
    - "rename to Moti" → {"action": "change_name", "newName": "Moti", "confidence": 100}`;
  } else if (context === 'milking') {
    systemMessage = `You are an expert at extracting milking information from text prompts.
    Analyze the user's message and extract milking-related information.

    Return ONLY a JSON object with the following structure:
    {
      "animalName": "exact animal name as written by user or null",
      "quantity": "exact milk quantity number as written or null",
      "unit": "exact unit as written (liter/litre/kg/gallon/etc) or null",
      "session": "morning|afternoon|evening|null",
      "date": "exact date as written or null",
      "action": "save|add|record|other",
      "confidence": "confidence level 1-100"
    }

    Examples:
    - "Bella gave 15 liters milk this morning" → {"animalName": "Bella", "quantity": "15", "unit": "liters", "session": "morning", "action": "record", "confidence": 95}
    - "Morning milking: 20 liters from Moti" → {"animalName": "Moti", "quantity": "20", "unit": "liters", "session": "morning", "action": "record", "confidence": 95}
    - "Record 12 kg milk from evening session" → {"quantity": "12", "unit": "kg", "session": "evening", "action": "record", "confidence": 90}`;
  } else if (context === 'pregnancy') {
    systemMessage = `You are an expert at extracting pregnancy information from text prompts.
    Analyze the user's message and extract pregnancy-related information.

    Return ONLY a JSON object with the following structure:
    {
      "animalName": "exact animal name as written by user or null",
      "animalId": "exact animal ID/tag as written by user or null",
      "sireName": "exact sire/bull name as written by user or null",
      "sireId": "exact sire ID/tag as written by user or null",
      "conceptionDate": "exact date as written or null",
      "status": "confirmed|suspected|not_pregnant|null",
      "isAICross": "boolean - true if AI cross/artificial insemination mentioned",
      "useAIPlans": "boolean - true if user asks for AI plans, diet plans, or health plans",
      "action": "save|add|record|other",
      "confidence": "confidence level 1-100"
    }

    Examples:
    - "Log a pregnancy for cow #123, sired by bull #5. Conception date was last Tuesday." → {"animalId": "#123", "sireId": "#5", "conceptionDate": "last Tuesday", "isAICross": false, "useAIPlans": false, "action": "record", "confidence": 95}
    - "Daisy is pregnant from an AI cross on June 1st. Please generate a diet plan for her." → {"animalName": "Daisy", "isAICross": true, "conceptionDate": "June 1st", "useAIPlans": true, "action": "record", "confidence": 95}
    - "Record mating between Bella and Titan on May 20, 2024. I think it's confirmed." → {"animalName": "Bella", "sireName": "Titan", "conceptionDate": "May 20, 2024", "status": "confirmed", "isAICross": false, "useAIPlans": false, "action": "record", "confidence": 95}
    - "Bessie is pregnant." → {"animalName": "Bessie", "isAICross": false, "useAIPlans": false, "action": "record", "confidence": 90}
    - "AI cross for goat Lily yesterday, and use AI plan" → {"animalName": "Lily", "isAICross": true, "conceptionDate": "yesterday", "useAIPlans": true, "action": "record", "confidence": 95}`;
  } else if (context === 'add_task') {
    systemMessage = `You are an expert at extracting task information from text prompts.
    Analyze the user's message and extract task-related information for farm management.

    Return ONLY a JSON object with the following structure:
    {
      "title": "exact task title/description as written by user or null",
      "description": "detailed description of the task or null",
      "assigneeName": "exact employee/person name as written by user or null",
      "dueDate": "exact date/time as written by user or null",
      "priority": "high|medium|low|null",
      "recurrence": "daily|weekly|monthly|none|null",
      "notes": "any additional notes or null",
      "action": "add|create|assign|other",
      "confidence": "confidence level 1-100"
    }

    Examples:
    - "Create a new task for John to clean the milking parlor tomorrow." → {"title": "clean the milking parlor", "assigneeName": "John", "dueDate": "tomorrow", "action": "create", "confidence": 95}
    - "Add a high priority task to check the water pump by Friday." → {"title": "check the water pump", "dueDate": "Friday", "priority": "high", "action": "add", "confidence": 95}
    - "Assign a task to Sarah to feed the animals daily." → {"title": "feed the animals", "assigneeName": "Sarah", "recurrence": "daily", "action": "assign", "confidence": 95}
    - "New task: Fix the fence. It's urgent." → {"title": "Fix the fence", "priority": "high", "action": "create", "confidence": 90}
    - "Delete the task" → {"action": "delete", "confidence": 95}
    - "Remove task 'fix the fence'" → {"title": "fix the fence", "action": "delete", "confidence": 95}
    - "Delete the cleaning task" → {"title": "cleaning", "action": "delete", "confidence": 90}
    - "Remove the milking task assigned to John" → {"title": "milking", "assigneeName": "John", "action": "delete", "confidence": 95}
    - "Remind Mike to vaccinate the cattle next week." → {"title": "vaccinate the cattle", "assigneeName": "Mike", "dueDate": "next week", "action": "create", "confidence": 95}
    - "Create weekly task for cleaning the barn." → {"title": "cleaning the barn", "recurrence": "weekly", "action": "create", "confidence": 95}`;
  } else if (context === 'animal_delete') {
    systemMessage = `You are an expert at analyzing animal deletion requests for a farm management system. Your primary task is to identify when a user wants to delete/remove an animal from their farm.

    CRITICAL: Only identify as deletion if the user explicitly uses deletion verbs (delete, remove, sold, died, cull, get rid of) with animal references.

    Return ONLY a JSON object with this structure:
    {
      "action": "delete_animal",
      "animalName": "exact animal name mentioned by user or null",
      "animalTagId": "animal tag/ID mentioned by user or null",
      "reason": "sold|died|transferred|culled|other|null",
      "confidence": "confidence level 1-100"
    }

    DELETION Examples:
    - "Delete my cow Daisy" → {"action": "delete_animal", "animalName": "Daisy", "animalTagId": null, "reason": null, "confidence": 98}
    - "I sold cow number 123 yesterday" → {"action": "delete_animal", "animalName": "cow number 123", "animalTagId": "123", "reason": "sold", "confidence": 95}
    - "Remove animal 45" → {"action": "delete_animal", "animalName": "animal 45", "animalTagId": "45", "reason": null, "confidence": 95}
    - "The goat named Lily died" → {"action": "delete_animal", "animalName": "Lily", "animalTagId": null, "reason": "died", "confidence": 96}
    - "I need to remove an animal from my list" → {"action": "delete_animal", "animalName": null, "animalTagId": null, "reason": null, "confidence": 90}
    - "Cull the sick sheep" → {"action": "delete_animal", "animalName": "sick sheep", "animalTagId": null, "reason": "culled", "confidence": 92}
    - "Get rid of cow 789" → {"action": "delete_animal", "animalName": "cow 789", "animalTagId": "789", "reason": null, "confidence": 94}

    URDU Examples:
    - "جانور نمبر 45 مر گیا ہے" → {"action": "delete_animal", "animalName": "جانور نمبر 45", "animalTagId": "45", "reason": "died", "confidence": 96}
    - "گائے ڈیزی کو حذف کریں" → {"action": "delete_animal", "animalName": "ڈیزی", "animalTagId": null, "reason": null, "confidence": 98}
    - "بکری فروخت کر دی" → {"action": "delete_animal", "animalName": "بکری", "animalTagId": null, "reason": "sold", "confidence": 95}

    NOT DELETION Examples:
    - "Show me the record for the animal I sold" → {"action": "view_record", "animalName": "the animal I sold", "animalTagId": null, "reason": "sold", "confidence": 85}
    - "Add a new cow" → {"action": "add_animal", "animalName": null, "animalTagId": null, "reason": null, "confidence": 95}
    - "Check health of Daisy" → {"action": "health_check", "animalName": "Daisy", "animalTagId": null, "reason": null, "confidence": 90}

    Be flexible with language variations and extract reasons when mentioned (sold, died, transferred, culled, etc.).`;
  } else if (context === 'expense_delete') {
    systemMessage = `You are an expert at analyzing expense deletion requests for a farm management system. Your primary task is to identify when a user wants to delete/remove an expense record from their farm.

    CRITICAL: Only identify as deletion if the user explicitly uses deletion verbs (delete, remove, vanish, eliminate, get rid of) with expense references.

    Return ONLY a JSON object with this structure:
    {
      "action": "delete_expense",
      "expenseDescription": "expense description mentioned by user or null",
      "expenseAmount": "expense amount mentioned by user or null",
      "expenseCategory": "expense category mentioned by user or null",
      "confidence": "confidence level 1-100"
    }

    DELETION Examples:
    - "Delete expense" → {"action": "delete_expense", "expenseDescription": null, "expenseAmount": null, "expenseCategory": null, "confidence": 95}
    - "Remove the feed expense" → {"action": "delete_expense", "expenseDescription": null, "expenseAmount": null, "expenseCategory": "feed", "confidence": 96}
    - "Delete the 5000 PKR expense" → {"action": "delete_expense", "expenseDescription": null, "expenseAmount": "5000", "expenseCategory": null, "confidence": 94}
    - "Remove expense for medicine" → {"action": "delete_expense", "expenseDescription": "medicine", "expenseAmount": null, "expenseCategory": null, "confidence": 93}
    - "Vanish the veterinary bill" → {"action": "delete_expense", "expenseDescription": "veterinary bill", "expenseAmount": null, "expenseCategory": "veterinary", "confidence": 92}
    - "Get rid of that expense" → {"action": "delete_expense", "expenseDescription": null, "expenseAmount": null, "expenseCategory": null, "confidence": 90}
    - "Remove exp" → {"action": "delete_expense", "expenseDescription": null, "expenseAmount": null, "expenseCategory": null, "confidence": 88}

    URDU Examples:
    - "خرچہ حذف کریں" → {"action": "delete_expense", "expenseDescription": null, "expenseAmount": null, "expenseCategory": null, "confidence": 95}
    - "فیڈ کا خرچہ ہٹا دیں" → {"action": "delete_expense", "expenseDescription": null, "expenseAmount": null, "expenseCategory": "feed", "confidence": 96}
    - "5000 روپے کا خرچہ حذف کریں" → {"action": "delete_expense", "expenseDescription": null, "expenseAmount": "5000", "expenseCategory": null, "confidence": 94}

    NOT DELETION Examples:
    - "Show me expenses" → {"action": "view_expenses", "expenseDescription": null, "expenseAmount": null, "expenseCategory": null, "confidence": 85}
    - "Add new expense" → {"action": "add_expense", "expenseDescription": null, "expenseAmount": null, "expenseCategory": null, "confidence": 95}
    - "Update expense amount" → {"action": "update_expense", "expenseDescription": null, "expenseAmount": null, "expenseCategory": null, "confidence": 90}

    Be flexible with language variations like "remove", "delete", "vanish", "eliminate", "get rid of" and expense references like "expense", "exp", "cost", "bill", "خرچہ", "لاگت".`;
  } else if (context === 'add_health_record') {
    systemMessage = `You are an expert at extracting health record information from text prompts.
    Analyze the user's message and extract health record-related information for farm animal management.

    IMPORTANT: This is for RECORDING completed health activities (vaccinations, treatments, medications),
    NOT for health checks or health assessments. Focus on extracting information about treatments that were already performed.

    Return ONLY a JSON object with the following structure:
    {
      "animalName": "exact animal name as written by user or null",
      "recordType": "vaccination|medication|surgery|checkup|birth|treatment|deworming|other",
      "recordOption": "specific vaccine/medicine/treatment name or null",
      "practitioner": "exact practitioner name as written or 'self' if user did it themselves",
      "date": "exact date as written or null (defaults to current date)",
      "notes": "any additional notes or auto-generated based on context",
      "action": "add|record|log|save|other",
      "confidence": "confidence level 1-100"
    }

    Record Types:
    - "vaccination": For vaccines and immunizations (FMD, HS, BQ, PPR, etc.)
    - "medication": For medicines and drugs (antibiotics, dewormers, etc.)
    - "surgery": For surgical procedures and operations
    - "checkup": For regular health examinations and check-ups
    - "birth": For recording birth events and deliveries
    - "treatment": For medical treatments and therapies
    - "deworming": For parasite control (can also be "medication")
    - "other": For any other health-related activities

    Examples:
    - "Record vaccination for Daisy with FMD vaccine. Dr. Ali administered it today." → {"animalName": "Daisy", "recordType": "vaccination", "recordOption": "FMD vaccine", "practitioner": "Dr. Ali", "date": "today", "action": "record", "confidence": 98}
    - "I gave deworming medicine to cow 123." → {"animalName": "cow 123", "recordType": "medication", "recordOption": "deworming medicine", "practitioner": "self", "action": "record", "confidence": 95}
    - "Vaccinated Lily." → {"animalName": "Lily", "recordType": "vaccination", "practitioner": "self", "action": "record", "confidence": 85}
    - "Log a deworming treatment for today." → {"recordType": "deworming", "date": "today", "practitioner": "self", "action": "log", "confidence": 90}
    - "Add medication record: Bella treated for fever with antibiotics by Dr. Khan yesterday." → {"animalName": "Bella", "recordType": "medication", "recordOption": "antibiotics", "practitioner": "Dr. Khan", "date": "yesterday", "notes": "treated for fever", "action": "add", "confidence": 95}
    - "Record surgery for Moti - castration performed by Dr. Smith." → {"animalName": "Moti", "recordType": "surgery", "recordOption": "castration", "practitioner": "Dr. Smith", "action": "record", "confidence": 95}
    - "Health checkup done for all cows today." → {"recordType": "checkup", "recordOption": "routine health examination", "practitioner": "self", "date": "today", "action": "record", "confidence": 90}
    - "Record birth: Daisy gave birth to a calf yesterday." → {"animalName": "Daisy", "recordType": "birth", "recordOption": "calf birth", "practitioner": "self", "date": "yesterday", "notes": "gave birth to a calf", "action": "record", "confidence": 95}
    - "Log births for today" → {"recordType": "birth", "date": "today", "practitioner": "self", "action": "log", "confidence": 90}
    - "Add surgery record" → {"recordType": "surgery", "practitioner": "self", "action": "add", "confidence": 90}
    - "Record checkup" → {"recordType": "checkup", "practitioner": "self", "action": "record", "confidence": 90}
    - "Record vaccination" → {"recordType": "vaccination", "practitioner": "self", "action": "record", "confidence": 90}`;
  } else {
    systemMessage = `You are an expert at extracting structured information from user prompts.
    Analyze the user's message and extract relevant information based on the context.`;
  }

  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        { role: 'system', content: systemMessage },
        { role: 'user', content: prompt }
      ],
      max_tokens: 500,
      temperature: 0.1,
    });

    const response = completion.choices[0]?.message?.content || '';
    console.log('AI Analysis Response:', response);

    // Try to parse JSON from response
    const jsonMatch = response.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }

    return null;
  } catch (error) {
    console.error('Error in AI analysis:', error);
    throw error;
  }
};

// Analyze task deletion requests
const analyzeTaskDeletionRequest = async (prompt) => {
  const systemMessage = `You are an AI assistant that analyzes task deletion requests for a farm management system.

Extract the following information from the user's request:
- taskTitle: The title or name of the task to delete (if specified)
- taskId: The ID of the task to delete (if specified)
- assigneeName: The name of the person assigned to the task (if specified)
- action: Should always be "delete" for deletion requests
- confidence: Your confidence level (0-100) that this is a task deletion request

Return ONLY a JSON object with these fields. If a field is not mentioned, omit it from the JSON.

Examples:
- "Delete the task" → {"action": "delete", "confidence": 95}
- "Remove task 'fix the fence'" → {"taskTitle": "fix the fence", "action": "delete", "confidence": 95}
- "Delete the cleaning task" → {"taskTitle": "cleaning", "action": "delete", "confidence": 90}
- "Remove the milking task assigned to John" → {"taskTitle": "milking", "assigneeName": "John", "action": "delete", "confidence": 95}
- "Delete task with ID abc123" → {"taskId": "abc123", "action": "delete", "confidence": 95}

Be flexible with language variations like "remove", "delete", "cancel", "eliminate" etc.`;

  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        { role: 'system', content: systemMessage },
        { role: 'user', content: prompt }
      ],
      max_tokens: 300,
      temperature: 0.1,
    });

    const response = completion.choices[0]?.message?.content || '';
    console.log('Task Deletion AI Analysis Response:', response);

    // Try to parse JSON from response
    const jsonMatch = response.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }

    return null;
  } catch (error) {
    console.error('Error in task deletion AI analysis:', error);
    throw error;
  }
};

// Helper function to get gestation period by species
const getGestationPeriod = (species) => {
  const gestationPeriods = {
    'cow': 280,
    'cattle': 280,
    'buffalo': 310,
    'goat': 150,
    'sheep': 150,
    'pig': 114,
    'horse': 340,
    'donkey': 365
  };

  return gestationPeriods[species.toLowerCase()] || 280; // Default to cow gestation
};

// Helper function to determine pregnancy stage
const getPregnancyStage = (week, totalWeeks) => {
  const earlyStage = Math.floor(totalWeeks * 0.33);
  const midStage = Math.floor(totalWeeks * 0.66);

  if (week <= earlyStage) return 'early';
  if (week <= midStage) return 'mid';
  return 'late';
};

// Generate AI-powered diet plan for pregnant animal
const generateAIDietPlan = async (animalData, language = 'en') => {
  if (!openai) {
    const success = initializeOpenAI();
    if (!success) {
      throw new Error('OpenAI client not initialized - API key missing');
    }
  }

  const species = animalData.species || 'cow';
  const gestationDays = getGestationPeriod(species);
  const gestationWeeks = Math.ceil(gestationDays / 7);

  const systemMessage = `You are a veterinary nutritionist expert specializing in pregnant animal diets.
  Generate a comprehensive weekly diet plan for a pregnant ${species} for ${gestationWeeks} weeks of gestation. The response MUST be in ${language === 'ur' ? 'Urdu' : 'English'}.

  Animal Details:
  - Species: ${species}
  - Age: ${animalData.age || 'unknown'}
  - Weight: ${animalData.weight || 'unknown'} kg
  - Health Status: ${animalData.healthStatus || 'unknown'}

  Return ONLY a JSON array with ${gestationWeeks} objects, one for each week:
  [
    {
      "week": 1,
      "stage": "early|mid|late",
      "title": "Week X: Descriptive title for this stage",
      "description": "Detailed nutrition focus for this week",
      "nutrients": ["key nutrients needed this week"],
      "foods": ["specific foods and quantities recommended"]
    }
  ]

  Guidelines:
  - Early stage (first third): Focus on maintaining body condition, basic nutrition
  - Mid stage (second third): Increase protein and energy, fetal development support
  - Late stage (final third): High energy, calcium, prepare for lactation
  - Be specific about quantities and types of feed
  - Consider the animal's species-specific nutritional needs.
  - Include supplements if necessary.
  - All text in the response (titles, descriptions, nutrients, foods) must be in ${language === 'ur' ? 'Urdu' : 'English'}.`;

  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        { role: 'system', content: systemMessage },
        { role: 'user', content: `Generate a complete diet plan for pregnant ${species}. The response must be in ${language === 'ur' ? 'Urdu' : 'English'}.` }
      ],
      max_tokens: 4000,
      temperature: 0.3,
    });

    const response = completion.choices[0]?.message?.content || '';
    console.log('Diet plan AI response:', response.substring(0, 500) + '...');

    // Try to parse JSON from response
    const jsonMatch = response.match(/\[[\s\S]*\]/);
    if (jsonMatch) {
      const dietPlan = JSON.parse(jsonMatch[0]);

      // Add stage information to each week
      return dietPlan.map(week => ({
        ...week,
        stage: getPregnancyStage(week.week, gestationWeeks)
      }));
    }

    return null;
  } catch (error) {
    console.error('Error generating diet plan:', error);
    throw error;
  }
};

// Generate AI-powered health plan for pregnant animal
const generateAIHealthPlan = async (animalData, language = 'en') => {
  if (!openai) {
    const success = initializeOpenAI();
    if (!success) {
      throw new Error('OpenAI client not initialized - API key missing');
    }
  }

  const species = animalData.species || 'cow';
  const gestationDays = getGestationPeriod(species);
  const gestationWeeks = Math.ceil(gestationDays / 7);

  const systemMessage = `You are a veterinary expert specializing in pregnant animal health management.
  Generate a comprehensive weekly health plan for a pregnant ${species} for ${gestationWeeks} weeks of gestation. The response MUST be in ${language === 'ur' ? 'Urdu' : 'English'}.

  Animal Details:
  - Species: ${species}
  - Age: ${animalData.age || 'unknown'}
  - Weight: ${animalData.weight || 'unknown'} kg
  - Health Status: ${animalData.healthStatus || 'unknown'}
  - Latest Health Check: ${animalData.latestHealthCheck || 'none'}

  Return ONLY a JSON array with ${gestationWeeks} objects, one for each week:
  [
    {
      "week": 1,
      "stage": "early|mid|late",
      "title": "Week X: Health focus title for this stage",
      "description": "Detailed health monitoring focus for this week",
      "checkups": ["specific health checks to perform this week"],
      "treatments": ["preventive treatments or interventions needed"]
    }
  ]

  Guidelines:
  - Early stage: Confirm pregnancy, basic health monitoring, nutrition adjustment
  - Mid stage: Fetal development monitoring, vaccination schedules, body condition
  - Late stage: Pre-birth preparation, udder development, birthing area preparation
  - Include vaccination schedules appropriate for the species
  - Consider parasite prevention and treatment.
  - Include behavioral and physical changes to monitor.
  - Be specific about timing and procedures.
  - All text in the response (titles, descriptions, checkups, treatments) must be in ${language === 'ur' ? 'Urdu' : 'English'}.`;

  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        { role: 'system', content: systemMessage },
        { role: 'user', content: `Generate a complete health plan for pregnant ${species}. The response must be in ${language === 'ur' ? 'Urdu' : 'English'}.` }
      ],
      max_tokens: 4000,
      temperature: 0.3,
    });

    const response = completion.choices[0]?.message?.content || '';
    console.log('Health plan AI response:', response.substring(0, 500) + '...');

    // Try to parse JSON from response
    const jsonMatch = response.match(/\[[\s\S]*\]/);
    if (jsonMatch) {
      const healthPlan = JSON.parse(jsonMatch[0]);

      // Add stage information to each week
      return healthPlan.map(week => ({
        ...week,
        stage: getPregnancyStage(week.week, gestationWeeks)
      }));
    }

    return null;
  } catch (error) {
    console.error('Error generating health plan:', error);
    throw error;
  }
};

module.exports = {
  initializeOpenAI,
  analyzePromptWithAI,
  analyzeTaskDeletionRequest,
  generateAIDietPlan,
  generateAIHealthPlan,
  getGestationPeriod
};
