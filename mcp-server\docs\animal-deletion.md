# Animal Deletion Feature

## Overview
The animal deletion feature allows users to remove animal records from their farm management system using natural language commands in both English and Roman Urdu. The system provides a visual animal selection interface when no specific animal is mentioned.

## Key Features

### 1. Natural Language Processing
The AI system can understand various deletion requests:

**English Examples:**
- "Delete my cow Daisy"
- "Remove animal number 123"
- "I sold the goat named <PERSON>"
- "The cow died this morning"
- "Get rid of animal 45"
- "I need to remove an animal"

**Urdu Examples:**
- "جانور نمبر 45 مر گیا ہے" (Animal number 45 has died)
- "گائے ڈیزی کو حذف کریں" (Delete cow Daisy)
- "بکری فروخت کر دی" (Sold the goat)

### 2. Reason Detection
The system automatically detects and records the reason for deletion:
- **sold** - When animal was sold
- **died** - When animal died
- **transferred** - When animal was moved
- **culled** - When animal was culled
- **other** - For other reasons

### 3. Visual Animal Selection
When no specific animal is mentioned, the system displays a visual list of all animals with:
- Animal images (or placeholder if no image)
- Animal names and species
- Click-to-select functionality

### 4. No Confirmation Required
As per requirements, when a user clicks on an animal image for deletion, the animal is deleted immediately without additional confirmation prompts.

## Technical Implementation

### Request Flow
1. **Intent Detection**: AI analyzes the prompt for deletion intent
2. **Animal Identification**: System tries to match animal by name or tag ID
3. **Direct Deletion**: If animal found, delete immediately
4. **Visual Selection**: If no animal specified, show selection interface
5. **Click Deletion**: User clicks animal image → immediate deletion

### JSON Response Structure
```json
{
  "action": "delete_animal",
  "animalName": "string | null",
  "animalTagId": "string | null", 
  "reason": "sold|died|transferred|culled|other|null",
  "confidence": "number (1-100)"
}
```

### Database Operations
- Deletes animal from Firestore subcollection
- Updates farm animal count
- Logs activity in farm activities collection
- Maintains referential integrity

### Error Handling
- Animal not found → Show selection interface
- No animals in farm → Error message
- Database errors → User-friendly error messages
- Invalid requests → Help text with examples

## API Endpoints

### Main Chat Endpoint
**POST** `/open-ai-chat`

**Request Body:**
```json
{
  "prompt": "Delete my cow Daisy",
  "userId": "user-id",
  "language": "en|ur",
  "context": {},
  "farms": [{"id": "farm-id", "name": "Farm Name"}]
}
```

**Response (Direct Deletion):**
```json
{
  "message": "✅ Animal Successfully Deleted...",
  "animalData": {
    "id": "animal-id",
    "name": "Daisy",
    "species": "cow"
  },
  "deleted": true,
  "databaseId": "animal-id"
}
```

**Response (Selection Interface):**
```json
{
  "message": "🗑️ Delete Animal\n\nPlease select...",
  "animalImages": [
    {
      "imageUri": "image-url",
      "name": "Animal Name",
      "species": "cow",
      "id": "animal-id"
    }
  ],
  "context": {
    "action": "delete_animal",
    "farmId": "farm-id",
    "needsAnimalSelection": true
  }
}
```

## Security Considerations
- Only farm owners can delete animals
- User authentication required
- Farm ownership validation
- Activity logging for audit trail

## Multilingual Support
- English and Roman Urdu supported
- Automatic language detection
- Localized response messages
- Cultural context awareness

## Testing
Use the test file `test-animal-deletion.js` to verify functionality:

```bash
node test-animal-deletion.js
```

## Integration with Frontend
The frontend chat interface automatically handles:
- Displaying animal images in a grid
- Click handlers for animal selection
- Success/error message display
- Consistent UI patterns with other features
