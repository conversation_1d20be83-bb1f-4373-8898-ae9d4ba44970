 // First, analyze the request type based on text and context
    const analyzeRequestType = (prompt, hasImage) => {
      if (!prompt && !hasImage) return 'unknown';

      const lowerPrompt = (prompt || '').toLowerCase();

      // PRIORITY ORDER MATTERS - Most specific patterns first!

      // Farm deletion patterns (HIGHEST PRIORITY - VERY SPECIFIC)
      // Must explicitly contain deletion verbs AND not contain creation verbs
      const deletionVerbs = ['delete', 'remove', 'demolish', 'destroy', 'eliminate'];
      const creationVerbs = ['add', 'create', 'new', 'save', 'register', 'establish'];
      const urduDeletionVerbs = ['حذف', 'ہٹا', 'منہدم'];
      const urduCreationVerbs = ['شامل', 'بنا', 'نیا', 'محفوظ'];

      const hasDeletionVerb = deletionVerbs.some(verb => lowerPrompt.includes(verb)) ||
                             urduDeletionVerbs.some(verb => lowerPrompt.includes(verb));
      const hasCreationVerb = creationVerbs.some(verb => lowerPrompt.includes(verb)) ||
                             urduCreationVerbs.some(verb => lowerPrompt.includes(verb));
      const hasFarmReference = lowerPrompt.includes('farm') || lowerPrompt.includes('فارم');

      if (hasDeletionVerb && hasFarmReference && !hasCreationVerb) {
        console.log('🗑️ FARM DELETION PATTERN MATCHED!');
        console.log('  - Has deletion verb:', hasDeletionVerb);
        console.log('  - Has farm reference:', hasFarmReference);
        console.log('  - No creation verb:', !hasCreationVerb);
        return 'delete_farm';
      }

      // Animal deletion patterns (VERY SPECIFIC - HIGH PRIORITY)
      const animalDeletionVerbs = ['delete', 'remove', 'sold', 'died', 'cull', 'get rid of', 'eliminate'];
      const urduAnimalDeletionVerbs = ['حذف', 'ہٹا', 'فروخت', 'مر گیا', 'مر گئی'];
      const animalReferences = ['animal', 'cow', 'goat', 'sheep', 'buffalo', 'bull', 'calf', 'جانور', 'گائے', 'بکری', 'بھیڑ', 'بھینس'];

      const hasAnimalDeletionVerb = animalDeletionVerbs.some(verb => lowerPrompt.includes(verb)) ||
                                   urduAnimalDeletionVerbs.some(verb => lowerPrompt.includes(verb));
      const hasAnimalReference = animalReferences.some(ref => lowerPrompt.includes(ref));
      const hasAnimalCreationVerb = lowerPrompt.includes('add') || lowerPrompt.includes('create') ||
                                   lowerPrompt.includes('new') || lowerPrompt.includes('شامل') ||
                                   lowerPrompt.includes('بنا');

      // Special check for "died" pattern - very specific for animal deletion
      if (lowerPrompt.includes('died') && hasAnimalReference) {
        console.log('🗑️ ANIMAL DEATH PATTERN MATCHED!');
        return 'delete_animal';
      }

      if (hasAnimalDeletionVerb && hasAnimalReference && !hasAnimalCreationVerb) {
        console.log('🗑️ ANIMAL DELETION PATTERN MATCHED!');
        console.log('  - Has animal deletion verb:', hasAnimalDeletionVerb);
        console.log('  - Has animal reference:', hasAnimalReference);
        console.log('  - No creation verb:', !hasAnimalCreationVerb);
        return 'delete_animal';
      }

      // Animal addition patterns (VERY SPECIFIC)
      if (lowerPrompt.includes('add this animal') ||
          lowerPrompt.includes('animal name is') ||
          (lowerPrompt.includes('add animal') && !lowerPrompt.includes('farm'))) {
        return 'add_animal';
      }

      // Farm addition patterns (VERY SPECIFIC)
      if (lowerPrompt.includes('add this farm') ||
          lowerPrompt.includes('farm name is') ||
          lowerPrompt.includes('create farm') ||
          (lowerPrompt.includes('add') && lowerPrompt.includes('farm') &&
           (lowerPrompt.includes('name') || lowerPrompt.includes('acre') || lowerPrompt.includes('address')))) {
        return 'add_farm';
      }

      // Pregnancy patterns (VERY SPECIFIC - should come before health check)
      if (lowerPrompt.includes('log a new pregnancy') ||
          lowerPrompt.includes('log a pregnancy') ||
          lowerPrompt.includes('add pregnancy') ||
          lowerPrompt.includes('record pregnancy') ||
          lowerPrompt.includes('is pregnant') ||
          lowerPrompt.includes('record mating') ||
          lowerPrompt.includes('confirmed pregnancy') ||
          lowerPrompt.includes('ai cross') ||
          lowerPrompt.includes('artificial insemination') ||
          lowerPrompt.includes('mated with') ||
          lowerPrompt.includes('conception date') ||
          (lowerPrompt.includes('pregnant') && !lowerPrompt.includes('not pregnant'))) {
        return 'add_pregnancy';
      }

      // Health record patterns (VERY SPECIFIC - should come before general health check)
      if (lowerPrompt.includes('record vaccination') ||
          lowerPrompt.includes('log vaccination') ||
          lowerPrompt.includes('add vaccination') ||
          lowerPrompt.includes('record treatment') ||
          lowerPrompt.includes('log treatment') ||
          lowerPrompt.includes('add treatment') ||
          lowerPrompt.includes('record deworming') ||
          lowerPrompt.includes('log deworming') ||
          lowerPrompt.includes('add deworming') ||
          lowerPrompt.includes('record medication') ||
          lowerPrompt.includes('log medication') ||
          lowerPrompt.includes('add medication') ||
          lowerPrompt.includes('record surgery') ||
          lowerPrompt.includes('log surgery') ||
          lowerPrompt.includes('add surgery') ||
          lowerPrompt.includes('record checkup') ||
          lowerPrompt.includes('log checkup') ||
          lowerPrompt.includes('add checkup') ||
          lowerPrompt.includes('record birth') ||
          lowerPrompt.includes('log birth') ||
          lowerPrompt.includes('add birth') ||
          lowerPrompt.includes('record births') ||
          lowerPrompt.includes('log births') ||
          lowerPrompt.includes('add births') ||
          lowerPrompt.includes('add health record') ||
          lowerPrompt.includes('health record for') ||
          (lowerPrompt.includes('record') && (lowerPrompt.includes('vaccination') || lowerPrompt.includes('treatment') || lowerPrompt.includes('medicine') || lowerPrompt.includes('vaccine') || lowerPrompt.includes('medication') || lowerPrompt.includes('deworming') || lowerPrompt.includes('surgery') || lowerPrompt.includes('checkup') || lowerPrompt.includes('birth'))) ||
          // Past tense actions that indicate recording
          (lowerPrompt.includes('vaccinated') && !lowerPrompt.includes('health check')) ||
          (lowerPrompt.includes('gave medicine') && !lowerPrompt.includes('health check')) ||
          (lowerPrompt.includes('administered') && !lowerPrompt.includes('health check')) ||
          (lowerPrompt.includes('treated') && !lowerPrompt.includes('health check')) ||
          (lowerPrompt.includes('dewormed') && !lowerPrompt.includes('health check')) ||
          (lowerPrompt.includes('surgery performed') && !lowerPrompt.includes('health check')) ||
          (lowerPrompt.includes('gave birth') && !lowerPrompt.includes('health check')) ||
          (lowerPrompt.includes('delivered') && !lowerPrompt.includes('health check'))) {
        return 'add_health_record';
      }

      // Health check patterns
      if (lowerPrompt.includes('health check') ||
          lowerPrompt.includes('sick') ||
          lowerPrompt.includes('treatment') ||
          lowerPrompt.includes('disease') ||
          lowerPrompt.includes('symptoms')) {
        return 'health_check';
      }

      // Milking patterns
      if (lowerPrompt.includes('milk') ||
          lowerPrompt.includes('milking') ||
          lowerPrompt.includes('liter') ||
          lowerPrompt.includes('litre')) {
        return 'milking';
      }

      // Expense deletion patterns (VERY SPECIFIC)
      const expenseDeletionVerbs = ['delete', 'remove', 'vanish', 'eliminate', 'get rid of'];
      const urduExpenseDeletionVerbs = ['حذف', 'ہٹا', 'ختم'];
      const expenseReferences = ['expense', 'exp', 'cost', 'bill', 'payment', 'خرچہ', 'لاگت', 'بل'];

      const hasExpenseDeletionVerb = expenseDeletionVerbs.some(verb => lowerPrompt.includes(verb)) ||
                                    urduExpenseDeletionVerbs.some(verb => lowerPrompt.includes(verb));
      const hasExpenseReference = expenseReferences.some(ref => lowerPrompt.includes(ref));
      const hasExpenseCreationVerb = lowerPrompt.includes('add') || lowerPrompt.includes('create') ||
                                    lowerPrompt.includes('new') || lowerPrompt.includes('record') ||
                                    lowerPrompt.includes('شامل') || lowerPrompt.includes('بنا');

      if (hasExpenseDeletionVerb && hasExpenseReference && !hasExpenseCreationVerb) {
        console.log('🗑️ EXPENSE DELETION PATTERN MATCHED!');
        console.log('  - Has expense deletion verb:', hasExpenseDeletionVerb);
        console.log('  - Has expense reference:', hasExpenseReference);
        console.log('  - No creation verb:', !hasExpenseCreationVerb);
        return 'delete_expense';
      }

      // Task deletion patterns (VERY SPECIFIC)
      const hasTaskDeletionVerb = lowerPrompt.includes('delete') ||
                                 lowerPrompt.includes('remove') ||
                                 lowerPrompt.includes('cancel') ||
                                 lowerPrompt.includes('eliminate') ||
                                 lowerPrompt.includes('حذف') || // Urdu for delete
                                 lowerPrompt.includes('ہٹا'); // Urdu for remove

      const hasTaskReference = lowerPrompt.includes('task') ||
                              lowerPrompt.includes('کام'); // Urdu for task

      const hasTaskCreationVerb = lowerPrompt.includes('create') ||
                                 lowerPrompt.includes('add') ||
                                 lowerPrompt.includes('assign') ||
                                 lowerPrompt.includes('new') ||
                                 lowerPrompt.includes('بنا'); // Urdu for create

      if (hasTaskDeletionVerb && hasTaskReference && !hasTaskCreationVerb) {
        console.log('🗑️ TASK DELETION PATTERN MATCHED!');
        console.log('  - Has deletion verb:', hasTaskDeletionVerb);
        console.log('  - Has task reference:', hasTaskReference);
        console.log('  - No creation verb:', !hasTaskCreationVerb);
        return 'delete_task';
      }

      // Task creation patterns (VERY SPECIFIC)
      if (lowerPrompt.includes('create a new task') ||
          lowerPrompt.includes('create a task') ||
          lowerPrompt.includes('add a task') ||
          lowerPrompt.includes('assign a task') ||
          lowerPrompt.includes('new task:') ||
          lowerPrompt.includes('task:') ||
          lowerPrompt.includes('remind') ||
          (lowerPrompt.includes('task') && (lowerPrompt.includes('for') || lowerPrompt.includes('to')))) {
        return 'add_task';
      }



      // Expense patterns
      if (lowerPrompt.includes('expense') ||
          lowerPrompt.includes('cost') ||
          lowerPrompt.includes('bill') ||
          lowerPrompt.includes('receipt') ||
          lowerPrompt.includes('payment')) {
        return 'expense';
      }

      return 'unknown';
    };
    module.exports = {
  analyzeRequestType
    };